import { useState, useEffect } from 'react';
import { useAuthStore } from '../stores/authStore';
import { AssetUpload } from '../components/assets/AssetUpload';
import { TimelapseCreator } from '../components/timelapse/TimelapseCreator';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import { PhotoIcon, VideoCameraIcon, PlayIcon } from '@heroicons/react/24/outline';

interface Asset {
  id: string;
  name: string;
  type: 'image' | 'video' | 'timelapse';
  file_path: string;
  file_size: number;
  mime_type: string;
  metadata: any;
  status: string;
  visibility: string;
  created_at: string;
  created_by_name: string;
}

export function AssetsPage() {
  const { token, user } = useAuthStore();
  const [assets, setAssets] = useState<Asset[]>([]);
  const [loading, setLoading] = useState(true);
  const [showUpload, setShowUpload] = useState(false);
  const [showTimelapseCreator, setShowTimelapseCreator] = useState(false);
  const [filter, setFilter] = useState<'all' | 'image' | 'video' | 'timelapse'>('all');

  useEffect(() => {
    fetchAssets();
  }, [filter]);

  const fetchAssets = async () => {
    try {
      const params = new URLSearchParams();
      if (filter !== 'all') {
        params.append('type', filter);
      }

      const response = await fetch(`/api/assets?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setAssets(data.assets);
      }
    } catch (error) {
      console.error('Failed to fetch assets:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUploadComplete = (newAssets: Asset[]) => {
    setAssets(prev => [...newAssets, ...prev]);
    setShowUpload(false);
  };

  const handleTimelapseCreated = (jobId: string) => {
    setShowTimelapseCreator(false);
    // Optionally show a success message or redirect to jobs page
    console.log('Timelapse job created:', jobId);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getAssetIcon = (type: string) => {
    switch (type) {
      case 'image':
        return <PhotoIcon className="h-8 w-8 text-blue-500" />;
      case 'video':
      case 'timelapse':
        return <VideoCameraIcon className="h-8 w-8 text-green-500" />;
      default:
        return <PhotoIcon className="h-8 w-8 text-gray-500" />;
    }
  };

  return (
    <div>
      <div className="md:flex md:items-center md:justify-between">
        <div className="min-w-0 flex-1">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
            Assets
          </h2>
        </div>
        <div className="mt-4 flex space-x-3 md:ml-4 md:mt-0">
          {user?.role === 'admin' && (
            <>
              <button
                onClick={() => setShowTimelapseCreator(true)}
                type="button"
                className="inline-flex items-center rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600"
              >
                <PlayIcon className="h-4 w-4 mr-2" />
                Create Timelapse
              </button>
              <button
                onClick={() => setShowUpload(true)}
                type="button"
                className="inline-flex items-center rounded-md bg-primary-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600"
              >
                Upload Asset
              </button>
            </>
          )}
        </div>
      </div>

      {/* Filters */}
      <div className="mt-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { key: 'all', label: 'All Assets' },
              { key: 'image', label: 'Images' },
              { key: 'video', label: 'Videos' },
              { key: 'timelapse', label: 'Timelapses' },
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setFilter(tab.key as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  filter === tab.key
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Assets Grid */}
      <div className="mt-8">
        {loading ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : assets.length === 0 ? (
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="text-center">
                <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-semibold text-gray-900">No assets</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {user?.role === 'admin'
                    ? 'Get started by uploading your first asset.'
                    : 'No assets have been published yet.'
                  }
                </p>
                {user?.role === 'admin' && (
                  <div className="mt-6">
                    <button
                      onClick={() => setShowUpload(true)}
                      type="button"
                      className="inline-flex items-center rounded-md bg-primary-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600"
                    >
                      Upload Asset
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {assets.map((asset) => (
              <div
                key={asset.id}
                className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
              >
                <div className="p-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      {getAssetIcon(asset.type)}
                    </div>
                    <div className="ml-4 flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {asset.name}
                      </p>
                      <p className="text-sm text-gray-500 capitalize">
                        {asset.type}
                      </p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{formatFileSize(asset.file_size)}</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        asset.visibility === 'public'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {asset.visibility}
                      </span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Created {new Date(asset.created_at).toLocaleDateString()}
                    </p>
                    {asset.created_by_name && (
                      <p className="text-xs text-gray-500">
                        by {asset.created_by_name}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Upload Modal */}
      {showUpload && (
        <AssetUpload
          onUploadComplete={handleUploadComplete}
          onClose={() => setShowUpload(false)}
        />
      )}

      {/* Timelapse Creator Modal */}
      {showTimelapseCreator && (
        <TimelapseCreator
          onTimelapseCreated={handleTimelapseCreated}
          onClose={() => setShowTimelapseCreator(false)}
        />
      )}
    </div>
  );
}
