import { Router } from 'express';
import { z } from 'zod';
import { asyncHand<PERSON>, createError } from '../middleware/errorHandler';
import { AuthenticatedRequest, requireRole } from '../middleware/auth';
import { query } from '../database/connection';
import { logger } from '../utils/logger';

const router = Router();

const createJobSchema = z.object({
  type: z.string().min(1),
  payload: z.object({}).optional(),
  assetIds: z.array(z.string().uuid()).optional(),
});

// Get jobs (filtered by tenant)
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { type, status, page = '1', limit = '20' } = req.query;
  
  let queryText = `
    SELECT j.id, j.type, j.status, j.payload, j.result, j.error_message, 
           j.progress, j.started_at, j.completed_at, j.created_at, j.updated_at,
           u.name as created_by_name
    FROM jobs j
    LEFT JOIN users u ON j.created_by = u.id
    WHERE j.tenant_id = $1
  `;
  let queryParams = [req.user?.tenantId];
  let paramCount = 2;

  if (type) {
    queryText += ` AND j.type = $${paramCount++}`;
    queryParams.push(type);
  }

  if (status) {
    queryText += ` AND j.status = $${paramCount++}`;
    queryParams.push(status);
  }

  queryText += ` ORDER BY j.created_at DESC`;

  // Pagination
  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const offset = (pageNum - 1) * limitNum;

  queryText += ` LIMIT $${paramCount++} OFFSET $${paramCount++}`;
  queryParams.push(limitNum, offset);

  const result = await query(queryText, queryParams);

  // Get total count
  let countQuery = `
    SELECT COUNT(*) as total
    FROM jobs j
    WHERE j.tenant_id = $1
  `;
  let countParams = [req.user?.tenantId];
  let countParamCount = 2;

  if (type) {
    countQuery += ` AND j.type = $${countParamCount++}`;
    countParams.push(type);
  }

  if (status) {
    countQuery += ` AND j.status = $${countParamCount++}`;
    countParams.push(status);
  }

  const countResult = await query(countQuery, countParams);
  const total = parseInt(countResult.rows[0].total);

  res.json({
    jobs: result.rows,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total,
      pages: Math.ceil(total / limitNum),
    }
  });
}));

// Get job by ID
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  
  const result = await query(
    `SELECT j.id, j.type, j.status, j.payload, j.result, j.error_message, 
            j.progress, j.started_at, j.completed_at, j.created_at, j.updated_at,
            u.name as created_by_name
     FROM jobs j
     LEFT JOIN users u ON j.created_by = u.id
     WHERE j.id = $1 AND j.tenant_id = $2`,
    [id, req.user?.tenantId]
  );

  if (result.rows.length === 0) {
    throw createError('Job not found', 404);
  }

  // Get associated assets
  const assetsResult = await query(
    `SELECT a.id, a.name, a.type, a.file_path
     FROM assets a
     JOIN job_assets ja ON a.id = ja.asset_id
     WHERE ja.job_id = $1`,
    [id]
  );

  res.json({
    job: {
      ...result.rows[0],
      assets: assetsResult.rows
    }
  });
}));

// Create job (admin only)
router.post('/', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = createJobSchema.parse(req.body);

  // Validate asset IDs if provided
  if (validatedData.assetIds && validatedData.assetIds.length > 0) {
    const assetsResult = await query(
      `SELECT id FROM assets WHERE id = ANY($1) AND tenant_id = $2`,
      [validatedData.assetIds, req.user?.tenantId]
    );

    if (assetsResult.rows.length !== validatedData.assetIds.length) {
      throw createError('One or more assets not found', 404);
    }
  }

  // Create job
  const jobResult = await query(
    `INSERT INTO jobs (tenant_id, type, payload, created_by) 
     VALUES ($1, $2, $3, $4) 
     RETURNING id, type, status, payload, progress, created_at`,
    [
      req.user?.tenantId,
      validatedData.type,
      JSON.stringify(validatedData.payload || {}),
      req.user?.id
    ]
  );

  const job = jobResult.rows[0];

  // Associate assets with job if provided
  if (validatedData.assetIds && validatedData.assetIds.length > 0) {
    const jobAssetValues = validatedData.assetIds.map(assetId => `('${job.id}', '${assetId}')`).join(', ');
    await query(`INSERT INTO job_assets (job_id, asset_id) VALUES ${jobAssetValues}`);
  }

  // TODO: Add job to BullMQ queue for processing

  logger.info(`Job created: ${validatedData.type} (${job.id}) by user ${req.user?.id}`);

  res.status(201).json({
    job
  });
}));

// Cancel job
router.post('/:id/cancel', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Check if job exists and belongs to user's tenant
  const existingJob = await query(
    'SELECT id, status FROM jobs WHERE id = $1 AND tenant_id = $2',
    [id, req.user?.tenantId]
  );

  if (existingJob.rows.length === 0) {
    throw createError('Job not found', 404);
  }

  const job = existingJob.rows[0];

  if (job.status === 'completed' || job.status === 'failed') {
    throw createError('Cannot cancel completed or failed job', 409);
  }

  // Update job status
  const result = await query(
    `UPDATE jobs SET status = 'failed', error_message = 'Cancelled by user', completed_at = NOW() 
     WHERE id = $1 
     RETURNING id, type, status, error_message, completed_at`,
    [id]
  );

  // TODO: Cancel job in BullMQ queue

  logger.info(`Job cancelled: ${id} by user ${req.user?.id}`);

  res.json({
    job: result.rows[0]
  });
}));

// Retry job
router.post('/:id/retry', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Check if job exists and belongs to user's tenant
  const existingJob = await query(
    'SELECT id, status, type, payload FROM jobs WHERE id = $1 AND tenant_id = $2',
    [id, req.user?.tenantId]
  );

  if (existingJob.rows.length === 0) {
    throw createError('Job not found', 404);
  }

  const job = existingJob.rows[0];

  if (job.status !== 'failed') {
    throw createError('Can only retry failed jobs', 409);
  }

  // Reset job status
  const result = await query(
    `UPDATE jobs SET status = 'pending', error_message = NULL, progress = 0, 
                     started_at = NULL, completed_at = NULL 
     WHERE id = $1 
     RETURNING id, type, status, progress`,
    [id]
  );

  // TODO: Re-add job to BullMQ queue for processing

  logger.info(`Job retried: ${id} by user ${req.user?.id}`);

  res.json({
    job: result.rows[0]
  });
}));

export default router;
