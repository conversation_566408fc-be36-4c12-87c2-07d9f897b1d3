# Local Development Configuration
# Copy this to .env and update with your actual values

# Server Configuration
NODE_ENV=development
PORT=3001
API_BASE_URL=http://localhost:3001

# Database Configuration
DATABASE_URL=postgresql://eagleview:eagleview_dev_password@localhost:5432/eagleview

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Auth0 Configuration (UPDATE THESE)
AUTH0_DOMAIN=your-auth0-domain.auth0.com
AUTH0_AUDIENCE=https://api.eagleview.com
AUTH0_CLIENT_ID=your-auth0-client-id
AUTH0_CLIENT_SECRET=your-auth0-client-secret

# AWS S3 / MinIO Configuration (Local MinIO)
AWS_ACCESS_KEY_ID=eagleview
AWS_SECRET_ACCESS_KEY=eagleview_dev_password
AWS_REGION=us-east-1
S3_BUCKET=eagleview-assets
S3_ENDPOINT=http://localhost:9000

# FTP Configuration (for testing)
FTP_HOST=test.rebex.net
FTP_USER=demo
FTP_PASSWORD=password
FTP_SECURE=false

# FFmpeg Configuration
FFMPEG_PATH=ffmpeg

# Logging
LOG_LEVEL=debug

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:3000
