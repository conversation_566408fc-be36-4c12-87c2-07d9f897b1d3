import { Router } from 'express';
import { z } from 'zod';
import multer from 'multer';
import { async<PERSON><PERSON><PERSON>, createError } from '../middleware/errorHandler';
import { AuthenticatedRequest, requireRole } from '../middleware/auth';
import { query } from '../database/connection';
import { logger } from '../utils/logger';

const router = Router();

// Configure multer for file uploads
const upload = multer({
  dest: 'uploads/',
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'video/mp4'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type'));
    }
  },
});

const createAssetSchema = z.object({
  name: z.string().min(1).max(255),
  type: z.enum(['image', 'video', 'timelapse']),
  visibility: z.enum(['private', 'public']).default('private'),
  metadata: z.object({}).optional(),
});

const updateAssetSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  visibility: z.enum(['private', 'public']).optional(),
  status: z.enum(['active', 'archived', 'deleted']).optional(),
  metadata: z.object({}).optional(),
});

// Get assets (filtered by tenant and visibility)
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { type, status, visibility, page = '1', limit = '20' } = req.query;
  
  let queryText = `
    SELECT a.id, a.name, a.type, a.file_path, a.file_size, a.mime_type, 
           a.metadata, a.status, a.visibility, a.created_at, a.updated_at,
           u.name as created_by_name
    FROM assets a
    LEFT JOIN users u ON a.created_by = u.id
    WHERE a.tenant_id = $1
  `;
  let queryParams = [req.user?.tenantId];
  let paramCount = 2;

  // Clients can only see public assets
  if (req.user?.role === 'client') {
    queryText += ` AND a.visibility = 'public'`;
  }

  if (type) {
    queryText += ` AND a.type = $${paramCount++}`;
    queryParams.push(type);
  }

  if (status) {
    queryText += ` AND a.status = $${paramCount++}`;
    queryParams.push(status);
  }

  if (visibility && req.user?.role === 'admin') {
    queryText += ` AND a.visibility = $${paramCount++}`;
    queryParams.push(visibility);
  }

  queryText += ` ORDER BY a.created_at DESC`;

  // Pagination
  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const offset = (pageNum - 1) * limitNum;

  queryText += ` LIMIT $${paramCount++} OFFSET $${paramCount++}`;
  queryParams.push(limitNum, offset);

  const result = await query(queryText, queryParams);

  // Get total count
  let countQuery = `
    SELECT COUNT(*) as total
    FROM assets a
    WHERE a.tenant_id = $1
  `;
  let countParams = [req.user?.tenantId];
  let countParamCount = 2;

  if (req.user?.role === 'client') {
    countQuery += ` AND a.visibility = 'public'`;
  }

  if (type) {
    countQuery += ` AND a.type = $${countParamCount++}`;
    countParams.push(type);
  }

  if (status) {
    countQuery += ` AND a.status = $${countParamCount++}`;
    countParams.push(status);
  }

  if (visibility && req.user?.role === 'admin') {
    countQuery += ` AND a.visibility = $${countParamCount++}`;
    countParams.push(visibility);
  }

  const countResult = await query(countQuery, countParams);
  const total = parseInt(countResult.rows[0].total);

  res.json({
    assets: result.rows,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total,
      pages: Math.ceil(total / limitNum),
    }
  });
}));

// Get asset by ID
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  
  let queryText = `
    SELECT a.id, a.name, a.type, a.file_path, a.file_size, a.mime_type, 
           a.metadata, a.status, a.visibility, a.created_at, a.updated_at,
           u.name as created_by_name
    FROM assets a
    LEFT JOIN users u ON a.created_by = u.id
    WHERE a.id = $1 AND a.tenant_id = $2
  `;
  let queryParams = [id, req.user?.tenantId];

  // Clients can only see public assets
  if (req.user?.role === 'client') {
    queryText += ` AND a.visibility = 'public'`;
  }

  const result = await query(queryText, queryParams);

  if (result.rows.length === 0) {
    throw createError('Asset not found', 404);
  }

  res.json({
    asset: result.rows[0]
  });
}));

// Upload asset
router.post('/', requireRole(['admin']), upload.single('file'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.file) {
    throw createError('No file uploaded', 400);
  }

  const validatedData = createAssetSchema.parse({
    ...req.body,
    metadata: req.body.metadata ? JSON.parse(req.body.metadata) : {},
  });

  // TODO: Upload file to S3/MinIO and get the file path
  const filePath = `/uploads/${req.file.filename}`;

  const result = await query(
    `INSERT INTO assets (tenant_id, name, type, file_path, file_size, mime_type, metadata, visibility, created_by) 
     VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) 
     RETURNING id, name, type, file_path, file_size, mime_type, metadata, status, visibility, created_at`,
    [
      req.user?.tenantId,
      validatedData.name,
      validatedData.type,
      filePath,
      req.file.size,
      req.file.mimetype,
      JSON.stringify(validatedData.metadata || {}),
      validatedData.visibility,
      req.user?.id
    ]
  );

  logger.info(`Asset uploaded: ${validatedData.name} by user ${req.user?.id}`);

  res.status(201).json({
    asset: result.rows[0]
  });
}));

// Update asset
router.put('/:id', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const validatedData = updateAssetSchema.parse(req.body);

  // Check if asset exists and belongs to user's tenant
  const existingAsset = await query(
    'SELECT id FROM assets WHERE id = $1 AND tenant_id = $2',
    [id, req.user?.tenantId]
  );

  if (existingAsset.rows.length === 0) {
    throw createError('Asset not found', 404);
  }

  const updateFields = [];
  const updateValues = [];
  let paramCount = 1;

  if (validatedData.name) {
    updateFields.push(`name = $${paramCount++}`);
    updateValues.push(validatedData.name);
  }

  if (validatedData.visibility) {
    updateFields.push(`visibility = $${paramCount++}`);
    updateValues.push(validatedData.visibility);
  }

  if (validatedData.status) {
    updateFields.push(`status = $${paramCount++}`);
    updateValues.push(validatedData.status);
  }

  if (validatedData.metadata) {
    updateFields.push(`metadata = $${paramCount++}`);
    updateValues.push(JSON.stringify(validatedData.metadata));
  }

  if (updateFields.length === 0) {
    throw createError('No fields to update', 400);
  }

  updateValues.push(id);

  const result = await query(
    `UPDATE assets SET ${updateFields.join(', ')} WHERE id = $${paramCount} 
     RETURNING id, name, type, file_path, file_size, mime_type, metadata, status, visibility, created_at, updated_at`,
    updateValues
  );

  logger.info(`Asset updated: ${id} by user ${req.user?.id}`);

  res.json({
    asset: result.rows[0]
  });
}));

// Delete asset
router.delete('/:id', requireRole(['admin']), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Check if asset exists and belongs to user's tenant
  const existingAsset = await query(
    'SELECT id, name, file_path FROM assets WHERE id = $1 AND tenant_id = $2',
    [id, req.user?.tenantId]
  );

  if (existingAsset.rows.length === 0) {
    throw createError('Asset not found', 404);
  }

  // TODO: Delete file from S3/MinIO

  await query('DELETE FROM assets WHERE id = $1', [id]);

  logger.info(`Asset deleted: ${existingAsset.rows[0].name} (${id}) by user ${req.user?.id}`);

  res.status(204).send();
}));

export default router;
