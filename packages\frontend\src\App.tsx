import { useAuth0 } from '@auth0/auth0-react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useEffect } from 'react'
import { useAuthStore } from './stores/authStore'
import { LoadingSpinner } from './components/ui/LoadingSpinner'
import { Layout } from './components/layout/Layout'
import { LoginPage } from './pages/LoginPage'
import { DashboardPage } from './pages/DashboardPage'
import { AssetsPage } from './pages/AssetsPage'
import { JobsPage } from './pages/JobsPage'
import { UsersPage } from './pages/UsersPage'
import { TenantsPage } from './pages/TenantsPage'

function App() {
  const { isLoading, isAuthenticated, getAccessTokenSilently, user } = useAuth0()
  const { setToken, setUser, user: storeUser } = useAuthStore()

  useEffect(() => {
    const initializeAuth = async () => {
      if (isAuthenticated && user) {
        try {
          const token = await getAccessTokenSilently()
          setToken(token)
          
          // Call backend to sync user data
          const response = await fetch('/api/auth/callback', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`,
            },
            body: JSON.stringify({
              auth0Id: user.sub,
              email: user.email,
              name: user.name,
            }),
          })

          if (response.ok) {
            const userData = await response.json()
            setUser(userData.user)
          }
        } catch (error) {
          console.error('Failed to initialize auth:', error)
        }
      }
    }

    initializeAuth()
  }, [isAuthenticated, user, getAccessTokenSilently, setToken, setUser])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!isAuthenticated) {
    return <LoginPage />
  }

  if (!storeUser) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <Layout>
      <Routes>
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        <Route path="/dashboard" element={<DashboardPage />} />
        <Route path="/assets" element={<AssetsPage />} />
        <Route path="/jobs" element={<JobsPage />} />
        {storeUser.role === 'admin' && (
          <>
            <Route path="/users" element={<UsersPage />} />
            <Route path="/tenants" element={<TenantsPage />} />
          </>
        )}
        <Route path="*" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </Layout>
  )
}

export default App
